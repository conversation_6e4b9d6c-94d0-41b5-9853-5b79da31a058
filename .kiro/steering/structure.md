# Project Structure

## Root Directory
```
├── src/                    # Source code
├── prisma/                 # Database schema and migrations
├── public/                 # Static assets
├── docs/                   # Documentation
├── .kiro/                  # Kiro configuration
└── complete-architecture-prd.md # Complete product requirements
```

## Source Code Organization (`src/`)

### Frontend Structure
```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes (grouped)
│   │   ├── login/
│   │   └── register/
│   ├── dashboard/         # Protected dashboard routes
│   │   ├── athletes/      # Athlete management
│   │   ├── workouts/      # Workout builder and management
│   │   └── analytics/     # Performance analytics
│   ├── api/               # API routes (tRPC endpoints)
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # React components
│   ├── ui/               # Shadcn/ui base components
│   ├── workout-builder/  # Drag-and-drop workout components
│   ├── charts/           # Data visualization components
│   └── forms/            # Form components
├── lib/                  # Utilities and configurations
│   ├── auth/            # Authentication logic
│   ├── trpc/            # tRPC client/server setup
│   ├── db/              # Database connection
│   ├── validations/     # Zod schemas
│   └── utils/           # Helper functions
└── stores/              # Zustand state management
    ├── workout.ts       # Workout builder state
    ├── athletes.ts      # Athletes data state
    └── sync.ts          # Garmin sync state
```

### Backend Structure
```
src/server/
├── routers/              # tRPC routers
│   ├── auth.ts          # Authentication endpoints
│   ├── athletes.ts      # Athlete management
│   ├── workouts.ts      # Workout CRUD operations
│   └── garmin.ts        # Garmin integration
├── services/            # Business logic services
│   ├── GarminService.ts # Garmin API integration
│   ├── WorkoutService.ts # Workout operations
│   ├── AnalysisService.ts # Activity analysis
│   └── NotificationService.ts # Email/SMS notifications
├── jobs/               # Background job processors
│   ├── syncActivities.ts # Garmin activity sync
│   ├── sendWorkouts.ts  # Workout delivery
│   └── notifications.ts # Notification sending
└── middleware/         # Request middleware
    ├── auth.ts         # Authentication middleware
    ├── rateLimit.ts    # Rate limiting
    └── validation.ts   # Request validation
```

## Database Structure (`prisma/`)
```
prisma/
├── schema.prisma        # Main database schema
├── migrations/          # Database migrations
└── seed/               # Database seed scripts
    ├── coaches.ts      # Sample coaches
    ├── athletes.ts     # Sample athletes
    └── templates.ts    # Workout templates
```

## Key Architectural Patterns

### Route Organization
- **Route Groups**: `(auth)` for authentication pages
- **Protected Routes**: All dashboard routes require authentication
- **API Routes**: tRPC endpoints under `/api/trpc/`

### Component Architecture
- **UI Components**: Reusable Shadcn/ui components in `components/ui/`
- **Feature Components**: Domain-specific components grouped by feature
- **Layout Components**: Shared layouts and navigation

### Data Flow
- **Client State**: Zustand stores for UI state
- **Server State**: tRPC queries with React Query caching
- **Database**: Prisma ORM with PostgreSQL
- **Background Jobs**: Bull queues for async processing

### File Naming Conventions
- **Components**: PascalCase (e.g., `WorkoutBuilder.tsx`)
- **Pages**: lowercase with hyphens (e.g., `workout-builder/`)
- **Utilities**: camelCase (e.g., `formatDuration.ts`)
- **Types**: PascalCase with `.types.ts` suffix

### Import Organization
1. React and Next.js imports
2. Third-party library imports
3. Internal component imports
4. Utility and type imports
5. Relative imports last