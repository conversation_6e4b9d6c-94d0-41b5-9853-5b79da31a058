# Technology Stack

## Frontend
- **Framework**: Next.js 14 with App Router
- **UI Library**: Shadcn/ui components + Tailwind CSS
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod validation
- **Charts**: Recharts for data visualization
- **PWA**: Progressive Web App capabilities

## Backend
- **Runtime**: Node.js
- **API**: tRPC for type-safe APIs
- **Authentication**: NextAuth.js with Google OAuth + credentials
- **Database ORM**: Prisma
- **Background Jobs**: Bull Queue with Redis
- **File Upload**: Drag-and-drop with validation

## Database & Storage
- **Primary DB**: PostgreSQL (Neon)
- **Cache**: Redis (Upstash)
- **File Storage**: Cloudflare R2
- **Search**: PostgreSQL full-text search

## External Integrations
- **Garmin**: Connect API with OAuth 1.0
- **Email**: Resend for transactional emails
- **Payments**: Stripe for subscriptions
- **SMS**: Twilio for notifications

## Infrastructure
- **Frontend Hosting**: Vercel with Edge Network
- **Backend Hosting**: Railway
- **Monitoring**: Sentry + Better Stack
- **Analytics**: PostHog

## Development Commands

### Setup
```bash
npm install
npx prisma generate
npx prisma db push
```

### Development
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
```

### Database
```bash
npx prisma studio    # Open database GUI
npx prisma migrate dev # Run migrations
npx prisma db seed   # Seed database
```

### Testing
```bash
npm run test         # Run unit tests
npm run test:e2e     # Run E2E tests with Playwright
npm run test:ci      # Run all tests in CI mode
```

### Code Quality
```bash
npm run lint         # ESLint
npm run type-check   # TypeScript checking
npm run format       # Prettier formatting
```