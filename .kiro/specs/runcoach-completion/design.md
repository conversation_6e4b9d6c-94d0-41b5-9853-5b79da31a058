# Design Document

## Overview

This design document outlines the technical architecture and implementation approach for completing the RunCoach platform. The design focuses on building the missing core features while leveraging the existing Next.js 14 + tRPC + Prisma foundation.

The architecture emphasizes scalability, maintainability, and user experience, with particular attention to the drag-and-drop workout builder and real-time Garmin integration that differentiate RunCoach from competitors.

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        WEB[Next.js 14 App]
        PWA[Progressive Web App]
        MOBILE[Mobile Responsive]
    end
    
    subgraph "API Layer"
        TRPC[tRPC Router]
        AUTH[NextAuth.js]
        MIDDLEWARE[Rate Limiting]
    end
    
    subgraph "Business Logic"
        WORKOUT[Workout Service]
        GARMIN[Garmin Service]
        ANALYSIS[Analysis Service]
        NOTIFICATION[Notification Service]
        SUBSCRIPTION[Subscription Service]
    end
    
    subgraph "Background Processing"
        QUEUE[Bull Queue + Redis]
        SYNC[Activity Sync Jobs]
        NOTIFY[Notification Jobs]
        ANALYZE[Analysis Jobs]
    end
    
    subgraph "External Services"
        GARMIN_API[Garmin Connect API]
        STRIPE[Stripe API]
        EMAIL[Resend Email]
        STORAGE[Cloudflare R2]
    end
    
    subgraph "Data Layer"
        POSTGRES[(PostgreSQL)]
        REDIS[(Redis Cache)]
    end
    
    WEB --> TRPC
    TRPC --> WORKOUT
    WORKOUT --> POSTGRES
    GARMIN --> GARMIN_API
    QUEUE --> SYNC
    SYNC --> GARMIN_API
    NOTIFICATION --> EMAIL
    SUBSCRIPTION --> STRIPE
```

### Frontend Architecture

The frontend will be built using React components with a focus on reusability and performance:

```typescript
// Component Architecture
src/
├── app/
│   ├── dashboard/
│   │   ├── workouts/
│   │   │   ├── page.tsx              // Workout list
│   │   │   ├── new/page.tsx          // Workout builder
│   │   │   └── [id]/
│   │   │       ├── page.tsx          // Workout details
│   │   │       └── edit/page.tsx     // Edit workout
│   │   ├── athletes/
│   │   │   ├── page.tsx              // Athletes list
│   │   │   ├── new/page.tsx          // Add athlete
│   │   │   └── [id]/
│   │   │       ├── page.tsx          // Athlete profile
│   │   │       └── analytics/page.tsx // Athlete analytics
│   │   ├── templates/page.tsx        // Template library
│   │   ├── analytics/page.tsx        // Dashboard analytics
│   │   └── settings/page.tsx         // Account settings
├── components/
│   ├── workout-builder/
│   │   ├── WorkoutBuilder.tsx        // Main builder component
│   │   ├── BlockPalette.tsx          // Draggable blocks
│   │   ├── WorkoutTimeline.tsx       // Drop zone timeline
│   │   ├── BlockEditor.tsx           // Block configuration
│   │   └── WorkoutPreview.tsx        // Visual preview
│   ├── analytics/
│   │   ├── PerformanceChart.tsx      // Recharts components
│   │   ├── ComplianceMetrics.tsx     // Compliance visualization
│   │   └── ProgressTrends.tsx        // Trend analysis
│   ├── garmin/
│   │   ├── GarminConnect.tsx         // OAuth connection
│   │   ├── SyncStatus.tsx            // Sync status display
│   │   └── ActivityList.tsx          // Activity display
│   └── ui/                           // Shadcn/ui components
```

## Components and Interfaces

### 1. Workout Builder Component

The workout builder is the core differentiator of the platform. It uses React DnD for drag-and-drop functionality:

```typescript
// Workout Builder Types
interface WorkoutBlock {
  id: string
  type: 'warmup' | 'interval' | 'recovery' | 'cooldown' | 'steady'
  duration: number // seconds
  intensity: IntensityTarget
  repetitions: number
  description?: string
}

interface IntensityTarget {
  type: 'zone' | 'pace' | 'heartrate'
  min: number
  max: number
  unit: string
}

// Main Builder Component
const WorkoutBuilder: React.FC = () => {
  const [blocks, setBlocks] = useState<WorkoutBlock[]>([])
  const [selectedBlock, setSelectedBlock] = useState<string | null>(null)
  
  const handleDrop = (item: WorkoutBlock, index: number) => {
    const newBlocks = [...blocks]
    newBlocks.splice(index, 0, item)
    setBlocks(newBlocks)
  }
  
  return (
    <DndProvider backend={HTML5Backend}>
      <div className="grid grid-cols-12 gap-6">
        <div className="col-span-3">
          <BlockPalette />
        </div>
        <div className="col-span-6">
          <WorkoutTimeline 
            blocks={blocks}
            onDrop={handleDrop}
            onSelect={setSelectedBlock}
          />
        </div>
        <div className="col-span-3">
          <BlockEditor 
            blockId={selectedBlock}
            onUpdate={updateBlock}
          />
        </div>
      </div>
    </DndProvider>
  )
}
```

### 2. Garmin Integration Service

The Garmin service handles OAuth 1.0 authentication and API interactions:

```typescript
// Garmin Service Architecture
class GarminService {
  private oauth1Client: OAuth1Client
  private baseUrl = 'https://connect.garmin.com'
  
  // OAuth Flow
  async initiateConnection(athleteId: string): Promise<{
    authUrl: string
    requestToken: string
    requestTokenSecret: string
  }> {
    const requestToken = await this.getRequestToken()
    const authUrl = `${this.baseUrl}/oauthConfirm?oauth_token=${requestToken.token}`
    
    // Store temporary tokens
    await redis.setex(
      `garmin:temp:${athleteId}`,
      600, // 10 minutes
      JSON.stringify(requestToken)
    )
    
    return { authUrl, ...requestToken }
  }
  
  async completeConnection(athleteId: string, verifier: string) {
    const tempTokens = await redis.get(`garmin:temp:${athleteId}`)
    if (!tempTokens) throw new Error('Token expired')
    
    const accessTokens = await this.getAccessToken(
      JSON.parse(tempTokens),
      verifier
    )
    
    // Encrypt and store permanent tokens
    await this.storeAthleteTokens(athleteId, accessTokens)
    
    // Schedule first sync
    await this.scheduleActivitySync(athleteId)
  }
  
  // Activity Sync
  async syncActivities(athleteId: string): Promise<Activity[]> {
    const tokens = await this.getAthleteTokens(athleteId)
    const activities = await this.fetchRecentActivities(tokens, 20)
    
    const newActivities = []
    for (const activity of activities) {
      const existing = await prisma.activity.findUnique({
        where: { garminActivityId: activity.activityId }
      })
      
      if (!existing) {
        const created = await this.createActivity(athleteId, activity)
        newActivities.push(created)
        
        // Queue analysis if workout was prescribed
        if (created.workoutId) {
          await this.queueAnalysis(created.id)
        }
      }
    }
    
    return newActivities
  }
  
  // Workout Push
  async sendWorkout(athleteId: string, workout: Workout): Promise<boolean> {
    const tokens = await this.getAthleteTokens(athleteId)
    const garminWorkout = this.convertToGarminFormat(workout)
    
    const response = await this.makeAuthenticatedRequest({
      url: `${this.baseUrl}/modern/proxy/workout-service/workout`,
      method: 'POST',
      data: garminWorkout
    }, tokens)
    
    if (response.success) {
      await prisma.workout.update({
        where: { id: workout.id },
        data: {
          garminWorkoutId: response.workoutId,
          garminSyncStatus: 'synced',
          sentAt: new Date()
        }
      })
    }
    
    return response.success
  }
}
```

### 3. Background Job Processing

Using Bull Queue with Redis for reliable background processing:

```typescript
// Job Queue Setup
interface JobTypes {
  'sync-activities': { athleteId: string }
  'send-workout': { workoutId: string }
  'analyze-activity': { activityId: string }
  'send-notification': { type: string, recipientId: string, data: any }
}

class JobProcessor {
  private queues: Map<string, Queue> = new Map()
  
  constructor() {
    this.setupQueues()
  }
  
  private setupQueues() {
    // Activity sync queue (every 30 minutes)
    const syncQueue = new Queue<JobTypes['sync-activities']>('sync-activities', {
      redis: { host: process.env.REDIS_HOST },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: 'exponential'
      }
    })
    
    syncQueue.process(5, this.processSyncJob.bind(this))
    this.queues.set('sync', syncQueue)
    
    // Analysis queue (immediate)
    const analysisQueue = new Queue<JobTypes['analyze-activity']>('analyze-activity', {
      redis: { host: process.env.REDIS_HOST }
    })
    
    analysisQueue.process(10, this.processAnalysisJob.bind(this))
    this.queues.set('analysis', analysisQueue)
  }
  
  private async processSyncJob(job: Job<JobTypes['sync-activities']>) {
    const { athleteId } = job.data
    const garminService = new GarminService()
    
    try {
      const newActivities = await garminService.syncActivities(athleteId)
      
      // Schedule next sync
      await this.scheduleActivitySync(athleteId)
      
      return { synced: newActivities.length }
    } catch (error) {
      console.error(`Sync failed for athlete ${athleteId}:`, error)
      throw error
    }
  }
  
  private async processAnalysisJob(job: Job<JobTypes['analyze-activity']>) {
    const { activityId } = job.data
    const analysisService = new AnalysisService()
    
    const analysis = await analysisService.analyzeActivity(activityId)
    
    if (analysis.complianceScore === 'D') {
      // Queue notification for poor compliance
      await this.queueNotification({
        type: 'poor-compliance',
        recipientId: analysis.coachId,
        data: { activityId, athleteName: analysis.athleteName }
      })
    }
    
    return analysis
  }
}
```

## Data Models

### Enhanced Database Schema

Building on the existing Prisma schema, we'll add these enhancements:

```prisma
// Additional models for completion

model WorkoutTemplate {
  id          String   @id @default(cuid())
  coachId     String?  // null = system template
  name        String
  description String?
  category    String   // "intervals", "endurance", "recovery"
  difficulty  String   // "beginner", "intermediate", "advanced"
  blocks      Json     // WorkoutBlock[]
  tags        String[]
  isPublic    Boolean  @default(false)
  usageCount  Int      @default(0)
  
  // Relations
  coach       Coach?   @relation(fields: [coachId], references: [id])
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("workout_templates")
}

model Notification {
  id          String   @id @default(cuid())
  coachId     String
  type        String   // "workout-completed", "poor-compliance", "sync-failed"
  title       String
  message     String
  data        Json?    // Additional context data
  read        Boolean  @default(false)
  
  // Relations
  coach       Coach    @relation(fields: [coachId], references: [id])
  
  createdAt   DateTime @default(now())
  
  @@map("notifications")
}

model FileUpload {
  id          String   @id @default(cuid())
  coachId     String
  athleteId   String?  // Optional - coach files vs athlete files
  filename    String
  originalName String
  mimeType    String
  size        Int
  url         String
  
  // Relations
  coach       Coach    @relation(fields: [coachId], references: [id])
  athlete     Athlete? @relation(fields: [athleteId], references: [id])
  
  createdAt   DateTime @default(now())
  
  @@map("file_uploads")
}

// Add to existing models
model Coach {
  // ... existing fields
  notifications Notification[]
  files        FileUpload[]
  templates    WorkoutTemplate[]
}

model Athlete {
  // ... existing fields
  files        FileUpload[]
}
```

## Error Handling

### Comprehensive Error Management

```typescript
// Custom Error Classes
class GarminApiError extends Error {
  constructor(
    public statusCode: number,
    public response: string,
    public endpoint: string
  ) {
    super(`Garmin API Error: ${statusCode} - ${response}`)
    this.name = 'GarminApiError'
  }
}

class WorkoutValidationError extends Error {
  constructor(public field: string, public value: any) {
    super(`Invalid workout data: ${field} = ${value}`)
    this.name = 'WorkoutValidationError'
  }
}

// Error Handler Middleware
export const errorHandler = (error: Error, req: any, res: any, next: any) => {
  console.error('Error:', error)
  
  // Log to Sentry in production
  if (process.env.NODE_ENV === 'production') {
    Sentry.captureException(error)
  }
  
  if (error instanceof GarminApiError) {
    return res.status(502).json({
      error: 'Garmin service unavailable',
      message: 'Please try again later'
    })
  }
  
  if (error instanceof WorkoutValidationError) {
    return res.status(400).json({
      error: 'Invalid workout data',
      field: error.field,
      message: error.message
    })
  }
  
  // Default error
  res.status(500).json({
    error: 'Internal server error',
    message: 'Something went wrong'
  })
}

// Retry Logic for External APIs
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxAttempts) break
      
      // Exponential backoff
      await new Promise(resolve => 
        setTimeout(resolve, delay * Math.pow(2, attempt - 1))
      )
    }
  }
  
  throw lastError!
}
```

## Testing Strategy

### Comprehensive Testing Approach

```typescript
// Unit Tests for Core Services
describe('GarminService', () => {
  let garminService: GarminService
  
  beforeEach(() => {
    garminService = new GarminService()
  })
  
  describe('syncActivities', () => {
    it('should sync new activities and skip existing ones', async () => {
      // Mock Garmin API response
      const mockActivities = [
        { activityId: '123', name: 'Morning Run' },
        { activityId: '124', name: 'Evening Run' }
      ]
      
      jest.spyOn(garminService, 'fetchRecentActivities')
        .mockResolvedValue(mockActivities)
      
      const result = await garminService.syncActivities('athlete-1')
      
      expect(result).toHaveLength(2)
      expect(result[0].garminActivityId).toBe('123')
    })
    
    it('should handle API errors gracefully', async () => {
      jest.spyOn(garminService, 'fetchRecentActivities')
        .mockRejectedValue(new GarminApiError(429, 'Rate limited', '/activities'))
      
      await expect(garminService.syncActivities('athlete-1'))
        .rejects.toThrow(GarminApiError)
    })
  })
})

// Integration Tests for API Endpoints
describe('/api/trpc/workouts', () => {
  it('should create workout with valid data', async () => {
    const workout = {
      name: 'Test Workout',
      athleteId: 'athlete-1',
      blocks: [
        {
          id: '1',
          type: 'warmup',
          duration: 600,
          intensity: { type: 'zone', min: 1, max: 2, unit: 'hr' }
        }
      ]
    }
    
    const response = await request(app)
      .post('/api/trpc/workouts.create')
      .send(workout)
      .expect(200)
    
    expect(response.body.result.data.name).toBe('Test Workout')
    expect(response.body.result.data.estimatedDuration).toBe(600)
  })
})

// E2E Tests with Playwright
test('workout builder flow', async ({ page }) => {
  await page.goto('/dashboard/workouts/new')
  
  // Drag warmup block
  await page.dragAndDrop('[data-testid="warmup-block"]', '[data-testid="timeline"]')
  
  // Configure block
  await page.click('[data-testid="block-1"]')
  await page.fill('[data-testid="duration-input"]', '10')
  
  // Save workout
  await page.fill('[data-testid="workout-name"]', 'E2E Test Workout')
  await page.click('[data-testid="save-button"]')
  
  // Verify creation
  await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
})
```

This design provides a comprehensive foundation for completing the RunCoach platform with all the missing features while maintaining code quality, scalability, and user experience standards.