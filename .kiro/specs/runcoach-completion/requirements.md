# Requirements Document

## Introduction

This document outlines the requirements to complete the RunCoach platform implementation. The platform is a SaaS B2B2C solution for Brazilian running coaches that replaces TrainingPeaks with 80% cost savings, modern UX, and native Garmin integration.

The current implementation has basic authentication, database structure, and dashboard functionality. This spec focuses on completing the core features that make RunCoach competitive with TrainingPeaks.

## Requirements

### Requirement 1: Visual Workout Builder

**User Story:** As a running coach, I want to create workouts using a drag-and-drop visual interface, so that I can quickly build structured training sessions without complex forms.

#### Acceptance Criteria

1. WHEN a coach accesses the workout builder THEN the system SHALL display a drag-and-drop interface with workout block components
2. WHEN a coach drags a workout block (warmup, interval, recovery, cooldown) THEN the system SHALL allow dropping it into the workout timeline
3. WHEN a coach configures a workout block THEN the system SHALL allow setting duration, intensity zones, pace targets, and descriptions
4. WHEN a coach saves a workout THEN the system SHALL calculate estimated duration and TSS automatically
5. WHEN a coach previews a workout THEN the system SHALL display a visual timeline with all blocks and their properties

### Requirement 2: Garmin Connect Integration

**User Story:** As a running coach, I want to automatically sync athlete activities from Garmin and send workouts to their devices, so that I can seamlessly manage training without manual data entry.

#### Acceptance Criteria

1. WHEN an athlete connects their Garmin account THEN the system SHALL complete OAuth 1.0 authentication flow
2. WHEN an athlete's Garmin is connected THEN the system SHALL automatically sync new activities every 30 minutes
3. WHEN a coach sends a workout to an athlete THEN the system SHALL push the workout to the athlete's Garmin device
4. WHEN activities are synced THEN the system SHALL store GPX data, metrics, and link to prescribed workouts
5. WHEN sync fails THEN the system SHALL retry with exponential backoff and log errors

### Requirement 3: Workout Templates Library

**User Story:** As a running coach, I want to create and use workout templates, so that I can quickly apply proven training patterns to multiple athletes.

#### Acceptance Criteria

1. WHEN a coach creates a workout THEN the system SHALL offer an option to save it as a template
2. WHEN a coach accesses templates THEN the system SHALL display personal and public template libraries
3. WHEN a coach selects a template THEN the system SHALL pre-populate the workout builder with template blocks
4. WHEN a coach searches templates THEN the system SHALL filter by category, difficulty, and tags
5. WHEN a template is used THEN the system SHALL increment its usage counter

### Requirement 4: Activity Analysis and Compliance

**User Story:** As a running coach, I want to automatically analyze how well athletes executed prescribed workouts, so that I can provide targeted feedback and adjust training plans.

#### Acceptance Criteria

1. WHEN an activity matches a prescribed workout THEN the system SHALL analyze compliance automatically
2. WHEN analyzing compliance THEN the system SHALL compare actual vs prescribed pace, heart rate, and duration
3. WHEN compliance analysis completes THEN the system SHALL assign a grade (A+, A, B, C, D)
4. WHEN compliance is poor THEN the system SHALL highlight specific deviations and suggest improvements
5. WHEN a coach views activity details THEN the system SHALL display compliance metrics and visual comparisons

### Requirement 5: Dashboard Analytics and Reporting

**User Story:** As a running coach, I want to view comprehensive analytics about my athletes' performance and training compliance, so that I can make data-driven coaching decisions.

#### Acceptance Criteria

1. WHEN a coach accesses analytics THEN the system SHALL display athlete performance trends over time
2. WHEN viewing analytics THEN the system SHALL show workout compliance rates, completion percentages, and progress metrics
3. WHEN filtering analytics THEN the system SHALL allow date ranges, specific athletes, and workout types
4. WHEN generating reports THEN the system SHALL create exportable summaries for athlete progress reviews
5. WHEN viewing team analytics THEN the system SHALL aggregate data across all athletes with comparative insights

### Requirement 6: Background Job Processing

**User Story:** As a system administrator, I want reliable background processing for Garmin sync, notifications, and data analysis, so that the platform operates smoothly without blocking user interactions.

#### Acceptance Criteria

1. WHEN activities need syncing THEN the system SHALL queue background jobs with proper retry logic
2. WHEN jobs fail THEN the system SHALL implement exponential backoff and error logging
3. WHEN processing workouts THEN the system SHALL queue analysis jobs after activity sync
4. WHEN sending notifications THEN the system SHALL queue email/SMS jobs with delivery tracking
5. WHEN jobs are running THEN the system SHALL provide status monitoring and manual retry capabilities

### Requirement 7: Subscription Management

**User Story:** As a running coach, I want to manage my subscription and billing through Stripe integration, so that I can upgrade/downgrade plans and handle payments securely.

#### Acceptance Criteria

1. WHEN a coach signs up THEN the system SHALL create a 14-day free trial without requiring payment
2. WHEN trial expires THEN the system SHALL prompt for subscription upgrade with Stripe checkout
3. WHEN subscription is active THEN the system SHALL enforce plan limits (athlete count, features)
4. WHEN a coach cancels THEN the system SHALL maintain access until period end and prevent renewals
5. WHEN payment fails THEN the system SHALL retry collection and notify the coach via email

### Requirement 8: Mobile-Responsive Workout Management

**User Story:** As a running coach, I want to manage workouts and view athlete data on mobile devices, so that I can coach effectively while traveling or at training sessions.

#### Acceptance Criteria

1. WHEN accessing on mobile THEN the system SHALL display responsive layouts for all key features
2. WHEN creating workouts on mobile THEN the system SHALL provide touch-friendly drag-and-drop interface
3. WHEN viewing athlete data on mobile THEN the system SHALL show condensed but complete information
4. WHEN receiving notifications on mobile THEN the system SHALL display properly formatted alerts
5. WHEN offline on mobile THEN the system SHALL cache recent data and sync when connection returns

### Requirement 9: Notification System

**User Story:** As a running coach, I want to receive notifications about athlete activities, workout completions, and system events, so that I can stay informed and respond quickly to coaching opportunities.

#### Acceptance Criteria

1. WHEN an athlete completes a workout THEN the system SHALL send notification to the coach
2. WHEN workout compliance is poor THEN the system SHALL alert the coach with specific details
3. WHEN Garmin sync fails THEN the system SHALL notify both coach and athlete
4. WHEN subscription expires THEN the system SHALL send reminder emails before and after expiration
5. WHEN configuring notifications THEN the system SHALL allow coaches to customize frequency and channels

### Requirement 10: File Storage and Management

**User Story:** As a running coach, I want to upload and manage files like training plans, athlete photos, and documents, so that I can maintain comprehensive athlete records.

#### Acceptance Criteria

1. WHEN uploading files THEN the system SHALL validate file types and sizes before storage
2. WHEN storing files THEN the system SHALL use secure cloud storage with proper access controls
3. WHEN accessing files THEN the system SHALL provide fast CDN delivery and thumbnail generation
4. WHEN deleting files THEN the system SHALL remove from storage and update database references
5. WHEN sharing files THEN the system SHALL generate secure, time-limited access URLs