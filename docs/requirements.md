# Requirements Document - Happy Runner

## Introduction

Este documento define os requisitos para o **Happy Runner**, uma solução simplificada que conecta Google Sheets ao Garmin Connect para automatizar o envio de treinos estruturados.

O sistema permite que usuários preencham uma planilha Google Sheets com treinos programados (data, pace, distância, tempo, etc.) e o sistema exporta automaticamente esses treinos para o calendário do atleta no Garmin Connect.

## Visão Geral da Solução

**Happy Runner** é uma ferramenta de automação que:
1. Lê treinos estruturados de uma planilha Google Sheets
2. Conecta com a conta Garmin Connect do usuário via OAuth
3. Converte os treinos para o formato Garmin
4. Envia os treinos para o calendário do Garmin Connect

## Requirements

### Requirement 1: Google Sheets Integration

**User Story:** Como usuário, quero conectar uma planilha Google Sheets ao sistema para que eu possa gerenciar meus treinos de forma simples e familiar.

#### Acceptance Criteria

1. WHEN o usuário acessa o sistema THEN o sistema SHALL permitir autenticação com Google OAuth 2.0
2. WHEN o usuário conecta uma planilha THEN o sistema SHALL validar o formato da planilha (colunas obrigatórias)
3. WHEN a planilha é conectada THEN o sistema SHALL ler os treinos programados automaticamente
4. WHEN novos treinos são adicionados na planilha THEN o sistema SHALL detectar as mudanças via polling ou webhooks
5. WHEN há erros na planilha THEN o sistema SHALL mostrar mensagens de validação específicas

### Requirement 2: Garmin Connect Integration

**User Story:** Como usuário, quero conectar minha conta Garmin Connect para que os treinos da planilha sejam enviados automaticamente para meu dispositivo.

#### Acceptance Criteria

1. WHEN o usuário conecta sua conta Garmin THEN o sistema SHALL completar o fluxo OAuth 1.0a
2. WHEN a conexão é estabelecida THEN o sistema SHALL armazenar os tokens de forma segura
3. WHEN um treino é programado THEN o sistema SHALL enviar o treino para o calendário Garmin
4. WHEN o envio falha THEN o sistema SHALL tentar novamente com backoff exponencial
5. WHEN há problemas de conexão THEN o sistema SHALL notificar o usuário e manter log de erros

### Requirement 3: Workout Format Conversion

**User Story:** Como usuário, quero que os treinos da planilha sejam convertidos automaticamente para o formato Garmin para que sejam enviados corretamente para meu dispositivo.

#### Acceptance Criteria

1. WHEN um treino é lido da planilha THEN o sistema SHALL validar os campos obrigatórios (data, tipo, duração)
2. WHEN os dados são válidos THEN o sistema SHALL converter para o formato de workout estruturado do Garmin
3. WHEN há treinos intervalados THEN o sistema SHALL criar as repetições e zonas de intensidade corretas
4. WHEN há treinos de pace THEN o sistema SHALL converter para as zonas de velocidade apropriadas
5. WHEN a conversão falha THEN o sistema SHALL registrar o erro e notificar o usuário

### Requirement 4: Dashboard e Monitoramento

**User Story:** Como usuário, quero visualizar o status dos meus treinos e conexões para que eu possa acompanhar se tudo está funcionando corretamente.

#### Acceptance Criteria

1. WHEN o usuário acessa o dashboard THEN o sistema SHALL mostrar o status da conexão Google Sheets
2. WHEN há treinos pendentes THEN o sistema SHALL mostrar a lista de treinos a serem enviados
3. WHEN há erros THEN o sistema SHALL mostrar logs de erro com detalhes
4. WHEN treinos são enviados THEN o sistema SHALL mostrar histórico de envios com status
5. WHEN há problemas de conexão THEN o sistema SHALL mostrar alertas e sugestões de solução

### Requirement 5: Formato da Planilha Google Sheets

**User Story:** Como usuário, quero usar uma planilha com formato padronizado para que o sistema possa ler meus treinos corretamente.

#### Acceptance Criteria

1. WHEN a planilha é conectada THEN o sistema SHALL validar as colunas obrigatórias: Data, Tipo, Duração, Intensidade
2. WHEN há colunas opcionais THEN o sistema SHALL aceitar: Descrição, Pace Target, Zona HR, Repetições
3. WHEN o formato está correto THEN o sistema SHALL processar todos os treinos válidos
4. WHEN há erros de formato THEN o sistema SHALL mostrar quais linhas têm problemas
5. WHEN a planilha é atualizada THEN o sistema SHALL detectar mudanças automaticamente

### Requirement 6: Background Processing

**User Story:** Como sistema, preciso processar treinos em background para que a experiência do usuário seja fluida.

#### Acceptance Criteria

1. WHEN novos treinos são detectados THEN o sistema SHALL processar em background jobs
2. WHEN jobs falham THEN o sistema SHALL implementar retry com backoff exponencial
3. WHEN há muitos treinos THEN o sistema SHALL processar em lotes para evitar sobrecarga
4. WHEN jobs estão rodando THEN o sistema SHALL mostrar status de processamento
5. WHEN há erros críticos THEN o sistema SHALL notificar o usuário imediatamente

## Formato da Planilha Google Sheets

### Estrutura Obrigatória

A planilha deve conter as seguintes colunas (nomes exatos):

| Coluna | Tipo | Obrigatório | Descrição |
|--------|------|-------------|-----------|
| Data | Date | Sim | Data do treino (formato: DD/MM/YYYY) |
| Tipo | Text | Sim | Tipo do treino (Corrida, Intervalado, Recovery, etc.) |
| Duração | Number | Sim | Duração em minutos |
| Intensidade | Text | Sim | Zona de intensidade (Z1, Z2, Z3, Z4, Z5) ou Pace |
| Descrição | Text | Não | Descrição detalhada do treino |
| Pace Target | Text | Não | Pace alvo (formato: MM:SS/km) |
| Repetições | Number | Não | Número de repetições (para intervalados) |
| Tempo Intervalo | Number | Não | Tempo de cada intervalo em segundos |
| Tempo Recovery | Number | Não | Tempo de recuperação em segundos |

### Exemplos de Treinos

**Treino Contínuo:**
- Data: 15/01/2025
- Tipo: Corrida Contínua
- Duração: 45
- Intensidade: Z2
- Descrição: Corrida aeróbica moderada

**Treino Intervalado:**
- Data: 17/01/2025
- Tipo: Intervalado
- Duração: 60
- Intensidade: Z4
- Repetições: 6
- Tempo Intervalo: 300 (5 minutos)
- Tempo Recovery: 120 (2 minutos)
- Descrição: 6x5min Z4 com 2min recovery