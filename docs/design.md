# Design Document - Happy Runner

## Overview

Este documento descreve a arquitetura técnica e implementação do **Happy Runner**, uma solução simplificada que conecta Google Sheets ao Garmin Connect para automatizar o envio de treinos estruturados.

A arquitetura foca em simplicidade, confiabilidade e facilidade de uso, utilizando Next.js 14 + tRPC + Prisma como base tecnológica.

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        WEB[Next.js 14 App]
        DASHBOARD[Dashboard UI]
        MOBILE[Mobile Responsive]
    end

    subgraph "API Layer"
        TRPC[tRPC Router]
        AUTH[NextAuth.js - Google OAuth]
        MIDDLEWARE[Rate Limiting]
    end

    subgraph "Business Logic"
        SHEETS[Google Sheets Service]
        GARMIN[Garmin Connect Service]
        CONVERTER[Workout Converter]
        SYNC[Sync Service]
    end

    subgraph "Background Processing"
        QUEUE[Bull Queue + Redis]
        POLL[Sheets Polling Jobs]
        SEND[Workout Send Jobs]
        RETRY[Retry Failed Jobs]
    end

    subgraph "External Services"
        GOOGLE_SHEETS[Google Sheets API]
        GARMIN_API[Garmin Connect API]
        EMAIL[Email Notifications]
    end

    subgraph "Data Layer"
        POSTGRES[(PostgreSQL)]
        REDIS[(Redis Cache)]
    end

    WEB --> TRPC
    TRPC --> SHEETS
    TRPC --> GARMIN
    SHEETS --> GOOGLE_SHEETS
    GARMIN --> GARMIN_API
    QUEUE --> POLL
    QUEUE --> SEND
    POLL --> GOOGLE_SHEETS
    SEND --> GARMIN_API
    CONVERTER --> POSTGRES
```

### Frontend Architecture

O frontend será construído com componentes React focados em simplicidade e usabilidade:

```typescript
// Component Architecture
src/
├── app/
│   ├── dashboard/
│   │   ├── page.tsx                  // Dashboard principal
│   │   ├── sheets/
│   │   │   ├── page.tsx              // Gerenciar planilhas
│   │   │   └── connect/page.tsx      // Conectar nova planilha
│   │   ├── garmin/
│   │   │   ├── page.tsx              // Status conexão Garmin
│   │   │   └── connect/page.tsx      // Conectar Garmin
│   │   ├── workouts/
│   │   │   ├── page.tsx              // Lista de treinos
│   │   │   └── [id]/page.tsx         // Detalhes do treino
│   │   ├── logs/page.tsx             // Logs e histórico
│   │   └── settings/page.tsx         // Configurações
├── components/
│   ├── sheets/
│   │   ├── SheetsConnect.tsx         // Conexão Google Sheets
│   │   ├── SheetsStatus.tsx          // Status da planilha
│   │   ├── WorkoutsList.tsx          // Lista de treinos da planilha
│   │   └── SheetsValidation.tsx      // Validação de formato
│   ├── garmin/
│   │   ├── GarminConnect.tsx         // OAuth Garmin
│   │   ├── GarminStatus.tsx          // Status conexão
│   │   ├── WorkoutSender.tsx         // Envio de treinos
│   │   └── SyncHistory.tsx           // Histórico de sincronização
│   ├── dashboard/
│   │   ├── StatusCards.tsx           // Cards de status
│   │   ├── RecentActivity.tsx        // Atividade recente
│   │   └── QuickActions.tsx          // Ações rápidas
│   └── ui/                           // Shadcn/ui components
```

## Components and Interfaces

### 1. Google Sheets Integration Component

O componente principal para conectar e gerenciar planilhas Google Sheets:

```typescript
// Google Sheets Types
interface SheetWorkout {
  id: string
  date: Date
  type: string
  duration: number // minutes
  intensity: string
  description?: string
  paceTarget?: string
  repetitions?: number
  intervalTime?: number // seconds
  recoveryTime?: number // seconds
}

interface SheetsConnection {
  id: string
  spreadsheetId: string
  spreadsheetName: string
  sheetName: string
  lastSync: Date
  status: 'connected' | 'error' | 'syncing'
  workoutCount: number
}

// Main Sheets Component
const SheetsConnect: React.FC = () => {
  const [connection, setConnection] = useState<SheetsConnection | null>(null)
  const [workouts, setWorkouts] = useState<SheetWorkout[]>([])
  const [isConnecting, setIsConnecting] = useState(false)

  const handleConnect = async (spreadsheetUrl: string) => {
    setIsConnecting(true)
    try {
      const result = await trpc.sheets.connect.mutate({ url: spreadsheetUrl })
      setConnection(result.connection)
      setWorkouts(result.workouts)
    } catch (error) {
      console.error('Failed to connect:', error)
    } finally {
      setIsConnecting(false)
    }
  }

  return (
    <div className="space-y-6">
      {!connection ? (
        <SheetsConnectionForm onConnect={handleConnect} loading={isConnecting} />
      ) : (
        <div className="space-y-4">
          <SheetsStatus connection={connection} />
          <WorkoutsList workouts={workouts} />
          <SyncControls connection={connection} />
        </div>
      )}
    </div>
  )
}
```

### 2. Garmin Integration Service

O serviço Garmin lida com autenticação OAuth 1.0a e envio de treinos:

```typescript
// Garmin Service Architecture
class GarminService {
  private oauth1Client: OAuth1Client
  private baseUrl = 'https://connect.garmin.com'

  // OAuth Flow
  async initiateConnection(userId: string): Promise<{
    authUrl: string
    requestToken: string
    requestTokenSecret: string
  }> {
    const requestToken = await this.getRequestToken()
    const authUrl = `${this.baseUrl}/oauthConfirm?oauth_token=${requestToken.token}`

    // Store temporary tokens
    await redis.setex(
      `garmin:temp:${userId}`,
      600, // 10 minutes
      JSON.stringify(requestToken)
    )

    return { authUrl, ...requestToken }
  }

  async completeConnection(userId: string, verifier: string) {
    const tempTokens = await redis.get(`garmin:temp:${userId}`)
    if (!tempTokens) throw new Error('Token expired')

    const accessTokens = await this.getAccessToken(
      JSON.parse(tempTokens),
      verifier
    )

    // Encrypt and store permanent tokens
    await this.storeUserTokens(userId, accessTokens)

    return { success: true }
  }

  // Workout Push - Core functionality
  async sendWorkout(userId: string, workout: SheetWorkout): Promise<boolean> {
    const tokens = await this.getUserTokens(userId)
    const garminWorkout = this.convertToGarminFormat(workout)

    const response = await this.makeAuthenticatedRequest({
      url: `${this.baseUrl}/modern/proxy/workout-service/workout`,
      method: 'POST',
      data: garminWorkout
    }, tokens)

    if (response.success) {
      await prisma.workout.update({
        where: { id: workout.id },
        data: {
          garminWorkoutId: response.workoutId,
          garminSyncStatus: 'synced',
          sentAt: new Date()
        }
      })
    }

    return response.success
  }

  // Convert sheet workout to Garmin format
  private convertToGarminFormat(workout: SheetWorkout): GarminWorkout {
    const garminWorkout: GarminWorkout = {
      workoutName: `${workout.type} - ${workout.date.toLocaleDateString()}`,
      description: workout.description || '',
      sport: 'RUNNING',
      steps: []
    }

    if (workout.repetitions && workout.intervalTime) {
      // Interval workout
      garminWorkout.steps = this.createIntervalSteps(workout)
    } else {
      // Continuous workout
      garminWorkout.steps = this.createContinuousSteps(workout)
    }

    return garminWorkout
  }
}
```

### 3. Background Job Processing

Usando Bull Queue com Redis para processamento confiável em background:

```typescript
// Job Queue Setup
interface JobTypes {
  'sync-sheets': { userId: string, connectionId: string }
  'send-workout': { workoutId: string, userId: string }
  'retry-failed': { originalJobId: string, jobType: string }
}

class JobProcessor {
  private queues: Map<string, Queue> = new Map()

  constructor() {
    this.setupQueues()
  }

  private setupQueues() {
    // Sheets sync queue (every 15 minutes)
    const sheetsQueue = new Queue<JobTypes['sync-sheets']>('sync-sheets', {
      redis: { host: process.env.REDIS_HOST },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: 'exponential'
      }
    })

    sheetsQueue.process(5, this.processSheetsSync.bind(this))
    this.queues.set('sheets', sheetsQueue)

    // Workout send queue (immediate)
    const workoutQueue = new Queue<JobTypes['send-workout']>('send-workout', {
      redis: { host: process.env.REDIS_HOST },
      defaultJobOptions: {
        attempts: 5,
        backoff: 'exponential'
      }
    })

    workoutQueue.process(3, this.processWorkoutSend.bind(this))
    this.queues.set('workout', workoutQueue)
  }

  private async processSheetsSync(job: Job<JobTypes['sync-sheets']>) {
    const { userId, connectionId } = job.data
    const sheetsService = new GoogleSheetsService()

    try {
      const newWorkouts = await sheetsService.syncWorkouts(connectionId)

      // Queue workout sends for new workouts
      for (const workout of newWorkouts) {
        if (workout.date >= new Date()) { // Only future workouts
          await this.queueWorkoutSend(workout.id, userId)
        }
      }

      // Schedule next sync
      await this.scheduleSheetsSync(userId, connectionId)

      return { synced: newWorkouts.length }
    } catch (error) {
      console.error(`Sheets sync failed for user ${userId}:`, error)
      throw error
    }
  }

  private async processWorkoutSend(job: Job<JobTypes['send-workout']>) {
    const { workoutId, userId } = job.data
    const garminService = new GarminService()

    try {
      const workout = await prisma.workout.findUnique({
        where: { id: workoutId }
      })

      if (!workout) {
        throw new Error(`Workout ${workoutId} not found`)
      }

      const success = await garminService.sendWorkout(userId, workout)

      if (!success) {
        throw new Error('Failed to send workout to Garmin')
      }

      return { success: true, workoutId }
    } catch (error) {
      console.error(`Workout send failed for ${workoutId}:`, error)
      throw error
    }
  }
}
```

## Data Models

### Simplified Database Schema

Schema simplificado para o Happy Runner:

```prisma
// Core models for Happy Runner

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  name              String?
  image             String?

  // Google OAuth
  googleAccessToken String?
  googleRefreshToken String?

  // Garmin OAuth
  garminAccessToken String?
  garminAccessTokenSecret String?
  garminConnected   Boolean  @default(false)

  // Relations
  sheetsConnections SheetsConnection[]
  workouts         Workout[]
  syncLogs         SyncLog[]

  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@map("users")
}

model SheetsConnection {
  id              String   @id @default(cuid())
  userId          String
  spreadsheetId   String
  spreadsheetName String
  sheetName       String
  lastSync        DateTime?
  status          String   @default("connected") // "connected", "error", "syncing"
  errorMessage    String?
  workoutCount    Int      @default(0)

  // Relations
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  workouts        Workout[]

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("sheets_connections")
}

model Workout {
  id              String   @id @default(cuid())
  userId          String
  connectionId    String

  // Workout data from sheets
  date            DateTime
  type            String
  duration        Int      // minutes
  intensity       String
  description     String?
  paceTarget      String?
  repetitions     Int?
  intervalTime    Int?     // seconds
  recoveryTime    Int?     // seconds

  // Garmin sync status
  garminWorkoutId String?
  garminSyncStatus String  @default("pending") // "pending", "synced", "failed"
  sentAt          DateTime?
  errorMessage    String?

  // Relations
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  connection      SheetsConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("workouts")
}

model SyncLog {
  id          String   @id @default(cuid())
  userId      String
  type        String   // "sheets-sync", "garmin-send", "error"
  status      String   // "success", "error", "warning"
  message     String
  data        Json?    // Additional context

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt   DateTime @default(now())

  @@map("sync_logs")
}
```

## Error Handling

### Tratamento de Erros Simplificado

```typescript
// Custom Error Classes
class GarminApiError extends Error {
  constructor(
    public statusCode: number,
    public response: string,
    public endpoint: string
  ) {
    super(`Garmin API Error: ${statusCode} - ${response}`)
    this.name = 'GarminApiError'
  }
}

class SheetsValidationError extends Error {
  constructor(public field: string, public row: number, public value: any) {
    super(`Invalid sheet data at row ${row}: ${field} = ${value}`)
    this.name = 'SheetsValidationError'
  }
}

class GoogleSheetsError extends Error {
  constructor(public statusCode: number, public message: string) {
    super(`Google Sheets API Error: ${statusCode} - ${message}`)
    this.name = 'GoogleSheetsError'
  }
}

// Error Handler Middleware
export const errorHandler = (error: Error, req: any, res: any, next: any) => {
  console.error('Error:', error)

  // Log to database for user visibility
  if (req.user?.id) {
    prisma.syncLog.create({
      data: {
        userId: req.user.id,
        type: 'error',
        status: 'error',
        message: error.message,
        data: { stack: error.stack }
      }
    }).catch(console.error)
  }

  if (error instanceof GarminApiError) {
    return res.status(502).json({
      error: 'Garmin service unavailable',
      message: 'Please try again later'
    })
  }

  if (error instanceof SheetsValidationError) {
    return res.status(400).json({
      error: 'Invalid sheet data',
      field: error.field,
      row: error.row,
      message: error.message
    })
  }

  if (error instanceof GoogleSheetsError) {
    return res.status(502).json({
      error: 'Google Sheets service unavailable',
      message: 'Please check your sheet permissions and try again'
    })
  }

  // Default error
  res.status(500).json({
    error: 'Internal server error',
    message: 'Something went wrong'
  })
}

// Retry Logic for External APIs
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error

      if (attempt === maxAttempts) break

      // Exponential backoff
      await new Promise(resolve =>
        setTimeout(resolve, delay * Math.pow(2, attempt - 1))
      )
    }
  }

  throw lastError!
}
```

## Testing Strategy

### Estratégia de Testes Simplificada

```typescript
// Unit Tests for Core Services
describe('GoogleSheetsService', () => {
  let sheetsService: GoogleSheetsService

  beforeEach(() => {
    sheetsService = new GoogleSheetsService()
  })

  describe('validateSheetFormat', () => {
    it('should validate correct sheet format', async () => {
      const mockData = [
        ['Data', 'Tipo', 'Duração', 'Intensidade'],
        ['15/01/2025', 'Corrida', '45', 'Z2'],
        ['17/01/2025', 'Intervalado', '60', 'Z4']
      ]

      const result = sheetsService.validateSheetFormat(mockData)

      expect(result.isValid).toBe(true)
      expect(result.workouts).toHaveLength(2)
    })

    it('should detect missing required columns', async () => {
      const mockData = [
        ['Data', 'Tipo'], // Missing Duration and Intensity
        ['15/01/2025', 'Corrida']
      ]

      const result = sheetsService.validateSheetFormat(mockData)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Missing required column: Duração')
    })
  })
})

// Integration Tests for API Endpoints
describe('/api/trpc/sheets', () => {
  it('should connect to valid Google Sheet', async () => {
    const sheetData = {
      spreadsheetId: 'test-sheet-id',
      sheetName: 'Treinos'
    }

    const response = await request(app)
      .post('/api/trpc/sheets.connect')
      .send(sheetData)
      .expect(200)

    expect(response.body.result.data.connection.status).toBe('connected')
    expect(response.body.result.data.workouts.length).toBeGreaterThan(0)
  })
})

// E2E Tests with Playwright
test('complete flow: connect sheets and send to Garmin', async ({ page }) => {
  await page.goto('/dashboard')

  // Connect Google Sheets
  await page.click('[data-testid="connect-sheets-button"]')
  await page.fill('[data-testid="sheet-url-input"]', 'https://docs.google.com/spreadsheets/d/test-id')
  await page.click('[data-testid="connect-button"]')

  // Verify connection
  await expect(page.locator('[data-testid="connection-status"]')).toContainText('Connected')

  // Connect Garmin
  await page.click('[data-testid="connect-garmin-button"]')
  // OAuth flow would be mocked in tests

  // Verify workouts are queued for sending
  await expect(page.locator('[data-testid="pending-workouts"]')).toBeVisible()
})
```

## Deployment

### Configuração de Produção

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    environment:
      - DATABASE_URL=************************************/happyrunner
      - REDIS_URL=redis://redis:6379
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GARMIN_CONSUMER_KEY=${GARMIN_CONSUMER_KEY}
      - GARMIN_CONSUMER_SECRET=${GARMIN_CONSUMER_SECRET}
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=happyrunner
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

Este design fornece uma base sólida para o Happy Runner, focando na simplicidade e funcionalidade essencial de conectar Google Sheets ao Garmin Connect.