{"name": "happy-runner", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^1.4.0", "@hookform/resolvers": "^3.3.4", "@prisma/client": "^5.9.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-card": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.20.5", "@trpc/client": "^10.45.1", "@trpc/next": "^10.45.1", "@trpc/react-query": "^10.45.1", "@trpc/server": "^10.45.1", "bull": "^4.12.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "googleapis": "^133.0.0", "lucide-react": "^0.323.0", "next": "14.1.0", "next-auth": "^4.24.6", "oauth-1.0a": "^2.2.6", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.49.3", "redis": "^4.6.13", "superjson": "^2.2.1", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/bull": "^4.10.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "prisma": "^5.9.1", "tailwindcss": "^3.3.0", "tsx": "^4.7.1", "typescript": "^5"}}